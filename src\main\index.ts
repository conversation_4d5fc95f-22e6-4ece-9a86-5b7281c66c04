import { app, shell, BrowserWindow } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import DatabaseManager from './database'
import { setupDatabaseIPC } from './ipc/database'
import { setupWindowControlsIPC } from './ipc/window-controls'

async function createWindow(): Promise<void> {
  // 初始化数据库
  const dbManager = DatabaseManager.getInstance()
  await dbManager.initialize()

  // 获取保存的窗口状态
  const savedState = await dbManager.getWindowState()

  // 创建浏览器窗口
  const mainWindow = new BrowserWindow({
    width: savedState?.bounds ? JSON.parse(savedState.bounds).width : 1400,
    height: savedState?.bounds ? JSON.parse(savedState.bounds).height : 900,
    minWidth: 800,
    minHeight: 600,
    x: savedState?.bounds ? JSON.parse(savedState.bounds).x : undefined,
    y: savedState?.bounds ? JSON.parse(savedState.bounds).y : undefined,
    show: false,
    frame: false, // 隐藏原生标题栏
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden',
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // 恢复窗口状态
  if (savedState) {
    if (savedState.isMaximized) {
      mainWindow.maximize()
    }
  }

  // 设置IPC处理程序
  setupDatabaseIPC()
  setupWindowControlsIPC(mainWindow)

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // 窗口状态变化监听
  const saveWindowState = async () => {
    try {
      const bounds = mainWindow.getBounds()
      await dbManager.saveWindowState({
        isMaximized: mainWindow.isMaximized() ? 1 : 0,
        isMinimized: mainWindow.isMinimized() ? 1 : 0,
        isFullscreen: mainWindow.isFullScreen() ? 1 : 0,
        isFocused: mainWindow.isFocused() ? 1 : 0,
        bounds: JSON.stringify(bounds)
      })
    } catch (error) {
      console.error('保存窗口状态失败:', error)
    }
  }

  // 监听窗口状态变化
  mainWindow.on('maximize', () => {
    mainWindow.webContents.send('window:state-changed', { isMaximized: true })
    saveWindowState()
  })

  mainWindow.on('unmaximize', () => {
    mainWindow.webContents.send('window:state-changed', { isMaximized: false })
    saveWindowState()
  })

  mainWindow.on('focus', () => {
    mainWindow.webContents.send('window:state-changed', { isFocused: true })
    saveWindowState()
  })

  mainWindow.on('blur', () => {
    mainWindow.webContents.send('window:state-changed', { isFocused: false })
    saveWindowState()
  })

  mainWindow.on('resize', () => {
    saveWindowState()
  })

  mainWindow.on('move', () => {
    saveWindowState()
  })

  // 基于electron-vite cli的渲染器热更新
  // 开发环境下加载远程URL，生产环境下加载本地HTML文件
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
// 某些API只能在此事件发生后使用
app.whenReady().then(async () => {
  // 为Windows设置应用程序用户模型ID
  electronApp.setAppUserModelId('com.aikairo.tbaw')

  // 在开发环境中通过F12默认打开或关闭开发者工具
  // 在生产环境中忽略CommandOrControl + R
  // 详见 https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  await createWindow()

  app.on('activate', async function () {
    // 在macOS上，当点击dock图标且没有其他窗口打开时，
    // 通常会在应用程序中重新创建一个窗口
    if (BrowserWindow.getAllWindows().length === 0) await createWindow()
  })
})

// 当所有窗口都被关闭时退出应用，除了在macOS上
// 在macOS上，应用及其菜单栏通常会保持活动状态，
// 直到用户使用Cmd + Q明确退出
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 应用退出前的清理工作
app.on('before-quit', async () => {
  try {
    const dbManager = DatabaseManager.getInstance()
    await dbManager.cleanup()
    await dbManager.close()
  } catch (error) {
    console.error('应用退出清理失败:', error)
  }
})

// 在这个文件中，你可以包含应用程序特定主进程的其余代码。
// 你也可以将它们放在单独的文件中，并在这里引入它们。
