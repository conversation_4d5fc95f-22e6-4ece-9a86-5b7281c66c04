import { ipcMain } from 'electron'
import DatabaseManager from '../database'
import type { ApiResponse, Tab, AppSettings, WindowState } from '../../renderer/src/types'

export function setupDatabaseIPC(): void {
  const dbManager = DatabaseManager.getInstance()

  // 保存标签页
  ipcMain.handle('db:save-tabs', async (_, tabs: Tab[]): Promise<ApiResponse> => {
    try {
      // 转换为数据库格式
      const dbTabs = tabs.map((tab) => ({
        id: tab.id,
        title: tab.title,
        active: tab.active ? 1 : 0,
        isDirty: tab.isDirty ? 1 : 0,
        isPinned: tab.isPinned ? 1 : 0,
        icon: tab.icon,
        closable: tab.closable ? 1 : 0,
        tooltip: tab.tooltip,
        lastModified: tab.lastModified?.toISOString(),
        metadata: tab.metadata ? JSON.stringify(tab.metadata) : undefined,
        path: tab.path,
        type: tab.type,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }))

      await dbManager.saveTabs(dbTabs)
      return { success: true }
    } catch (error) {
      console.error('保存标签页失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })

  // 获取标签页
  ipcMain.handle('db:get-tabs', async (): Promise<ApiResponse<Tab[]>> => {
    try {
      const dbTabs = await dbManager.getTabs()

      // 转换为应用格式
      const tabs: Tab[] = dbTabs.map((dbTab) => ({
        id: dbTab.id,
        title: dbTab.title,
        active: Boolean(dbTab.active),
        isDirty: Boolean(dbTab.isDirty),
        isPinned: Boolean(dbTab.isPinned),
        icon: dbTab.icon,
        closable: Boolean(dbTab.closable),
        tooltip: dbTab.tooltip,
        lastModified: dbTab.lastModified ? new Date(dbTab.lastModified) : undefined,
        metadata: dbTab.metadata ? JSON.parse(dbTab.metadata) : undefined,
        path: dbTab.path,
        type: dbTab.type as 'home' | 'folder' | 'file'
      }))

      return { success: true, data: tabs }
    } catch (error) {
      console.error('获取标签页失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })

  // 保存设置
  ipcMain.handle('db:save-setting', async (_, key: string, value: any): Promise<ApiResponse> => {
    try {
      await dbManager.saveSetting(key, value)
      return { success: true }
    } catch (error) {
      console.error('保存设置失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })

  // 获取设置
  ipcMain.handle(
    'db:get-setting',
    async (_, key: string, defaultValue?: any): Promise<ApiResponse<any>> => {
      try {
        const value = await dbManager.getSetting(key, defaultValue)
        return { success: true, data: value }
      } catch (error) {
        console.error('获取设置失败:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : '未知错误'
        }
      }
    }
  )

  // 获取所有设置
  ipcMain.handle('db:get-all-settings', async (): Promise<ApiResponse<AppSettings>> => {
    try {
      const settings = await dbManager.getAllSettings()
      return { success: true, data: settings as AppSettings }
    } catch (error) {
      console.error('获取所有设置失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })

  // 保存窗口状态
  ipcMain.handle('db:save-window-state', async (_, state: WindowState): Promise<ApiResponse> => {
    try {
      const dbState = {
        isMaximized: state.isMaximized ? 1 : 0,
        isMinimized: state.isMinimized ? 1 : 0,
        isFullscreen: state.isFullscreen ? 1 : 0,
        isFocused: state.isFocused ? 1 : 0,
        bounds: state.bounds ? JSON.stringify(state.bounds) : undefined
      }

      await dbManager.saveWindowState(dbState)
      return { success: true }
    } catch (error) {
      console.error('保存窗口状态失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })

  // 获取窗口状态
  ipcMain.handle('db:get-window-state', async (): Promise<ApiResponse<WindowState | null>> => {
    try {
      const dbState = await dbManager.getWindowState()

      if (!dbState) {
        return { success: true, data: null }
      }

      const state: WindowState = {
        isMaximized: Boolean(dbState.isMaximized),
        isMinimized: Boolean(dbState.isMinimized),
        isFullscreen: Boolean(dbState.isFullscreen),
        isFocused: Boolean(dbState.isFocused),
        bounds: dbState.bounds ? JSON.parse(dbState.bounds) : undefined
      }

      return { success: true, data: state }
    } catch (error) {
      console.error('获取窗口状态失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })

  // 数据库清理
  ipcMain.handle('db:cleanup', async (): Promise<ApiResponse> => {
    try {
      await dbManager.cleanup()
      return { success: true }
    } catch (error) {
      console.error('数据库清理失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  })
}
