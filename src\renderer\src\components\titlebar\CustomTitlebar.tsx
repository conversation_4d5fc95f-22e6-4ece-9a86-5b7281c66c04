import React, { useCallback } from 'react'
import { Settings, Plus, MoreHorizontal } from 'lucide-react'
import { cn } from '@/lib/utils'
import { TabList } from './TabList'
import { WindowControls } from './WindowControls'
import { useTitlebarStore } from '@/stores/titlebar-store'

interface CustomTitlebarProps {
  title?: string
  className?: string
  children?: React.ReactNode
}

export function CustomTitlebar({ title = '作家助手', className, children }: CustomTitlebarProps) {
  const {
    tabs,
    config,
    updateTab,
    removeTab,
    reorderTabs,
    duplicateTab,
    closeOtherTabs,
    closeTabsToRight,
    pinTab,
    unpinTab,
    addTab
  } = useTitlebarStore()

  const platform = window.api?.system.getPlatform()

  // 处理标签页操作
  const handleTabAction = useCallback(
    (action: string, tabId: string, payload?: any) => {
      switch (action) {
        case 'activate':
          updateTab(tabId, { active: true })
          // 设置窗口标题
          const tab = tabs.find((t) => t.id === tabId)
          if (tab) {
            window.api?.window.setTitle(`${tab.title} - ${title}`)
          }
          break

        case 'close':
          removeTab(tabId)
          break

        case 'pin':
          pinTab(tabId)
          break

        case 'unpin':
          unpinTab(tabId)
          break

        case 'duplicate':
          duplicateTab(tabId)
          break

        case 'reorder':
          if (payload?.targetId) {
            reorderTabs(tabId, payload.targetId)
          }
          break

        case 'close-others':
          closeOtherTabs(tabId)
          break

        case 'close-to-right':
          closeTabsToRight(tabId)
          break

        default:
          console.warn('未知的标签页操作:', action)
      }
    },
    [
      tabs,
      title,
      updateTab,
      removeTab,
      reorderTabs,
      duplicateTab,
      closeOtherTabs,
      closeTabsToRight,
      pinTab,
      unpinTab
    ]
  )

  // 处理标题栏右键菜单
  const handleTitlebarContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    window.api?.titlebar.showContextMenu(e.clientX, e.clientY)
  }, [])

  // 添加新标签页
  const handleAddTab = useCallback(() => {
    const newTab = {
      id: `tab-${Date.now()}`,
      title: '新建文档',
      type: 'file' as const,
      isDirty: false,
      isPinned: false,
      closable: true
    }
    addTab(newTab)
  }, [addTab])

  // macOS 特殊处理
  const isMacOS = platform === 'darwin'
  const titlebarHeight = isMacOS ? 28 : 32

  return (
    <div
      className={cn(
        'flex items-center bg-white border-b border-gray-200',
        'select-none relative z-50',
        className
      )}
      style={
        {
          height: titlebarHeight,
          WebkitAppRegion: 'drag'
        } as React.CSSProperties
      }
      onContextMenu={handleTitlebarContextMenu}
    >
      {/* 左侧：应用图标和标题 */}
      <div
        className={cn(
          'flex items-center gap-3 px-4 shrink-0',
          isMacOS && 'pl-20' // 为 macOS 交通灯按钮留空间
        )}
        style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}
      >
        {config.showIcon && (
          <div className="w-5 h-5 bg-gradient-to-br from-blue-500 to-blue-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">作</span>
          </div>
        )}

        {config.showTitle && <span className="text-sm font-medium text-gray-700">{title}</span>}
      </div>

      {/* 标签页区域 */}
      {tabs.length > 0 && (
        <>
          <div className="h-6 w-px bg-gray-200 mx-2" />
          <div className="flex-1 min-w-0 h-full">
            <TabList tabs={tabs} config={config} onTabAction={handleTabAction} className="h-full" />
          </div>
        </>
      )}

      {/* 中间：可拖拽区域 */}
      <div
        className="flex-1 h-full min-w-[100px]"
        style={{ WebkitAppRegion: 'drag' } as React.CSSProperties}
      />

      {/* 右侧：工具按钮和窗口控制 */}
      <div
        className="flex items-center h-full shrink-0"
        style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}
      >
        {/* 工具按钮 */}
        <div className="flex items-center gap-1 px-2">
          <button
            onClick={handleAddTab}
            className="h-7 w-7 p-0 hover:bg-gray-100 rounded flex items-center justify-center"
            aria-label="新建标签页"
          >
            <Plus className="h-4 w-4" />
          </button>

          <div className="h-4 w-px bg-gray-200 mx-1" />

          <button
            className="h-7 w-7 p-0 hover:bg-gray-100 rounded flex items-center justify-center"
            aria-label="设置"
          >
            <Settings className="h-4 w-4" />
          </button>

          <button
            className="h-7 w-7 p-0 hover:bg-gray-100 rounded flex items-center justify-center"
            aria-label="更多选项"
          >
            <MoreHorizontal className="h-4 w-4" />
          </button>
        </div>

        {!isMacOS && (
          <>
            <div className="h-6 w-px bg-gray-200" />
            <WindowControls />
          </>
        )}
      </div>

      {/* 自定义内容 */}
      {children}
    </div>
  )
}
