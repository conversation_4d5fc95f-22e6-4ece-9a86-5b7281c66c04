import { z } from 'zod'

// 标签页相关类型
export const TabSchema = z.object({
  id: z.string(),
  title: z.string(),
  active: z.boolean().default(false),
  isDirty: z.boolean().default(false),
  isPinned: z.boolean().default(false),
  icon: z.string().optional(),
  closable: z.boolean().default(true),
  tooltip: z.string().optional(),
  lastModified: z.date().optional(),
  metadata: z.record(z.any()).optional(),
  path: z.string().optional(), // 文件路径
  type: z.enum(['home', 'folder', 'file']).default('file')
})

// 窗口状态类型
export const WindowStateSchema = z.object({
  isMaximized: z.boolean(),
  isMinimized: z.boolean(),
  isFullscreen: z.boolean(),
  isFocused: z.boolean(),
  bounds: z
    .object({
      x: z.number(),
      y: z.number(),
      width: z.number(),
      height: z.number()
    })
    .optional()
})

// 应用设置类型
export const AppSettingsSchema = z.object({
  saveAppState: z.boolean().default(true),
  restoreWindowState: z.boolean().default(true),
  restoreTabs: z.boolean().default(true),
  maxRecentTabs: z.number().min(1).max(20).default(10),
  theme: z.enum(['light', 'dark', 'system']).default('system'),
  language: z.string().default('zh-CN')
})

// 标题栏配置类型
export const TitlebarConfigSchema = z.object({
  showIcon: z.boolean().default(true),
  showTitle: z.boolean().default(true),
  maxVisibleTabs: z.number().min(1).max(50).default(8),
  enableTabReordering: z.boolean().default(true),
  enableTabContextMenu: z.boolean().default(true),
  enableVirtualization: z.boolean().default(true),
  customStyles: z.record(z.string()).optional()
})

// API响应类型
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  timestamp: z.number().default(() => Date.now())
})

// 导出推断类型
export type Tab = z.infer<typeof TabSchema>
export type WindowState = z.infer<typeof WindowStateSchema>
export type AppSettings = z.infer<typeof AppSettingsSchema>
export type TitlebarConfig = z.infer<typeof TitlebarConfigSchema>
export type ApiResponse<T = any> = z.infer<typeof ApiResponseSchema> & {
  data?: T
}

// 数据库相关类型
export interface DatabaseTab {
  id: string
  title: string
  active: number // SQLite中的布尔值
  isDirty: number
  isPinned: number
  icon?: string
  closable: number
  tooltip?: string
  lastModified?: string
  metadata?: string // JSON字符串
  path?: string
  type: string
  createdAt: string
  updatedAt: string
}

export interface DatabaseSettings {
  key: string
  value: string // JSON字符串
  updatedAt: string
}

export interface DatabaseWindowState {
  id: number
  isMaximized: number
  isMinimized: number
  isFullscreen: number
  isFocused: number
  bounds?: string // JSON字符串
  updatedAt: string
}
