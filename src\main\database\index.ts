import Database from 'better-sqlite3'
import { app } from 'electron'
import { join } from 'path'
import { existsSync, mkdirSync } from 'fs'
import { runMigrations } from './migrations'
import type { DatabaseTab, DatabaseSettings, DatabaseWindowState } from '../../shared/types'

class DatabaseManager {
  private static instance: DatabaseManager
  private db: Database.Database | null = null

  private constructor() {
    // 私有构造函数，确保单例模式
  }

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager()
    }
    return DatabaseManager.instance
  }

  async initialize(): Promise<void> {
    try {
      // 确保数据目录存在
      const userDataPath = app.getPath('userData')
      const dbDir = join(userDataPath, 'database')

      if (!existsSync(dbDir)) {
        mkdirSync(dbDir, { recursive: true })
      }

      const dbPath = join(dbDir, 'tbaw.db')

      // 创建数据库连接
      this.db = new Database(dbPath)

      // 启用外键约束
      this.db.pragma('foreign_keys = ON')

      // 设置WAL模式以提高性能
      this.db.pragma('journal_mode = WAL')

      // 运行数据库迁移
      await runMigrations(this.db)

      console.log('Database initialized successfully:', dbPath)
    } catch (error) {
      console.error('Database initialization failed:', error)
      throw error
    }
  }

  getDatabase(): Database.Database {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }
    return this.db
  }

  // 标签页相关操作
  async saveTabs(tabs: DatabaseTab[]): Promise<void> {
    if (!this.db) throw new Error('数据库未初始化')

    const transaction = this.db.transaction(() => {
      // 清空现有标签页
      this.db!.prepare('DELETE FROM tabs').run()

      // 插入新标签页
      const insertTab = this.db!.prepare(`
        INSERT INTO tabs (
          id, title, active, isDirty, isPinned, icon, closable, 
          tooltip, lastModified, metadata, path, type, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      for (const tab of tabs) {
        insertTab.run(
          tab.id,
          tab.title,
          tab.active,
          tab.isDirty,
          tab.isPinned,
          tab.icon,
          tab.closable,
          tab.tooltip,
          tab.lastModified,
          tab.metadata,
          tab.path,
          tab.type,
          tab.createdAt,
          tab.updatedAt
        )
      }
    })

    transaction()
  }

  async getTabs(): Promise<DatabaseTab[]> {
    if (!this.db) throw new Error('数据库未初始化')

    const stmt = this.db.prepare('SELECT * FROM tabs ORDER BY createdAt ASC')
    return stmt.all() as DatabaseTab[]
  }

  // 设置相关操作
  async saveSetting(key: string, value: any): Promise<void> {
    if (!this.db) throw new Error('数据库未初始化')

    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value, updatedAt) 
      VALUES (?, ?, ?)
    `)

    stmt.run(key, JSON.stringify(value), new Date().toISOString())
  }

  async getSetting<T>(key: string, defaultValue?: T): Promise<T | undefined> {
    if (!this.db) throw new Error('数据库未初始化')

    const stmt = this.db.prepare('SELECT value FROM settings WHERE key = ?')
    const result = stmt.get(key) as DatabaseSettings | undefined

    if (result) {
      try {
        return JSON.parse(result.value)
      } catch (error) {
        console.error('解析设置值失败:', error)
        return defaultValue
      }
    }

    return defaultValue
  }

  async getAllSettings(): Promise<Record<string, any>> {
    if (!this.db) throw new Error('数据库未初始化')

    const stmt = this.db.prepare('SELECT key, value FROM settings')
    const results = stmt.all() as DatabaseSettings[]

    const settings: Record<string, any> = {}
    for (const result of results) {
      try {
        settings[result.key] = JSON.parse(result.value)
      } catch (error) {
        console.error(`解析设置 ${result.key} 失败:`, error)
      }
    }

    return settings
  }

  // 窗口状态相关操作
  async saveWindowState(state: Omit<DatabaseWindowState, 'id' | 'updatedAt'>): Promise<void> {
    if (!this.db) throw new Error('数据库未初始化')

    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO window_state (
        id, isMaximized, isMinimized, isFullscreen, isFocused, bounds, updatedAt
      ) VALUES (1, ?, ?, ?, ?, ?, ?)
    `)

    stmt.run(
      state.isMaximized,
      state.isMinimized,
      state.isFullscreen,
      state.isFocused,
      state.bounds,
      new Date().toISOString()
    )
  }

  async getWindowState(): Promise<DatabaseWindowState | undefined> {
    if (!this.db) throw new Error('数据库未初始化')

    const stmt = this.db.prepare('SELECT * FROM window_state WHERE id = 1')
    return stmt.get() as DatabaseWindowState | undefined
  }

  // 清理操作
  async cleanup(): Promise<void> {
    if (!this.db) return

    try {
      // 清理旧的标签页记录（保留最近30天）
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      this.db.prepare('DELETE FROM tabs WHERE updatedAt < ?').run(thirtyDaysAgo.toISOString())

      // 优化数据库
      this.db.pragma('optimize')

      console.log('Database cleanup completed')
    } catch (error) {
      console.error('Database cleanup failed:', error)
    }
  }

  async close(): Promise<void> {
    if (this.db) {
      this.db.close()
      this.db = null
      console.log('Database connection closed')
    }
  }
}

export default DatabaseManager
