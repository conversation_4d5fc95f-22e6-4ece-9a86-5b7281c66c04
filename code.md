基于最新的 Tailwind CSS v4 和 electron-vite 框架，这里是完整的重写方案（去除主题支持）：

## 完整的自定义标题栏解决方案（Tailwind v4 + electron-vite）

### 1. 项目初始化

```bash
# 使用 electron-vite 创建项目
npm create electron-vite@latest my-titlebar-app -- --template react-ts

cd my-titlebar-app

# 安装 Tailwind CSS v4 (最新版本)
npm install tailwindcss@latest @tailwindcss/vite@latest

# 安装其他依赖
npm install zod zustand react-window react-dnd react-dnd-html5-backend
npm install react-error-boundary lodash-es

# 安装 shadcn/ui
npx shadcn@latest init

# 添加需要的 shadcn 组件
npx shadcn@latest add button tabs separator context-menu dropdown-menu
```

### 2. 项目结构

```
src/
├── main/
│   ├── index.ts
│   └── ipc/
│       └── window-controls.ts
├── preload/
│   └── index.ts
├── renderer/
│   ├── src/
│   │   ├── components/
│   │   │   ├── titlebar/
│   │   │   │   ├── CustomTitlebar.tsx
│   │   │   │   ├── VirtualizedTabList.tsx
│   │   │   │   ├── WindowControls.tsx
│   │   │   │   └── TabItem.tsx
│   │   │   └── ui/ (shadcn components)
│   │   ├── hooks/
│   │   │   ├── useWindowControls.ts
│   │   │   ├── useTabManager.ts
│   │   │   └── usePerformanceMonitor.ts
│   │   ├── stores/
│   │   │   └── titlebar-store.ts
│   │   ├── types/
│   │   │   └── titlebar.ts
│   │   ├── lib/
│   │   │   └── utils.ts
│   │   ├── App.tsx
│   │   ├── main.tsx
│   │   └── globals.css
│   └── index.html
├── electron.vite.config.ts
└── package.json
```

### 3. electron-vite 配置

```typescript
// electron.vite.config.ts
import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()],
    build: {
      rollupOptions: {
        input: {
          index: resolve(__dirname, 'src/main/index.ts')
        }
      }
    }
  },
  preload: {
    plugins: [externalizeDepsPlugin()],
    build: {
      rollupOptions: {
        input: {
          index: resolve(__dirname, 'src/preload/index.ts')
        }
      }
    }
  },
  renderer: {
    resolve: {
      alias: {
        '@renderer': resolve('src/renderer/src'),
        '@': resolve('src/renderer/src')
      }
    },
    plugins: [react(), tailwindcss()],
    build: {
      rollupOptions: {
        input: {
          index: resolve(__dirname, 'src/renderer/index.html')
        }
      }
    }
  }
})
```

### 4. Tailwind CSS v4 配置

```css
/* src/renderer/src/globals.css */
@import 'tailwindcss';

@theme {
  /* 自定义颜色 */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-200: #bfdbfe;
  --color-blue-300: #93c5fd;
  --color-blue-400: #60a5fa;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;
  --color-blue-800: #1e40af;
  --color-blue-900: #1e3a8a;

  --color-red-50: #fef2f2;
  --color-red-100: #fee2e2;
  --color-red-200: #fecaca;
  --color-red-300: #fca5a5;
  --color-red-400: #f87171;
  --color-red-500: #ef4444;
  --color-red-600: #dc2626;
  --color-red-700: #b91c1c;
  --color-red-800: #991b1b;
  --color-red-900: #7f1d1d;

  --color-orange-400: #fb923c;

  /* 字体 */
  --font-family-sans:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);

  /* 间距 */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;

  /* 圆角 */
  --radius-sm: 0.125rem;
  --radius: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;

  /* 过渡 */
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

@layer base {
  html,
  body,
  #root {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  * {
    box-sizing: border-box;
  }

  /* 禁用文本选择 */
  .select-none {
    user-select: none;
  }

  /* 隐藏滚动条但保持滚动功能 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  /* 自定义滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: var(--color-gray-300) transparent;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--color-gray-300);
      border-radius: var(--radius-sm);

      &:hover {
        background: var(--color-gray-400);
      }
    }
  }
}

@layer components {
  /* 标题栏特定样式 */
  .titlebar-drag-region {
    -webkit-app-region: drag;
  }

  .titlebar-no-drag {
    -webkit-app-region: no-drag;
  }

  /* 标签页过渡动画 */
  .tab-transition {
    transition: all 0.15s var(--ease-out);

    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }
  }

  /* 窗口控制按钮样式 */
  .window-control-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2rem;
    width: 2rem;
    transition: background-color 0.15s var(--ease-out);

    &:hover {
      background-color: var(--color-gray-100);
    }

    &.close:hover {
      background-color: var(--color-red-500);
      color: white;
    }
  }

  /* 平台特定样式 */
  .platform-darwin .titlebar {
    height: 28px;
    padding-left: 78px;
  }

  .platform-win32 .titlebar,
  .platform-linux .titlebar {
    height: 32px;
  }

  /* 性能优化：GPU 加速 */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  /* 无障碍支持 */
  .focus-visible {
    outline: none;
    box-shadow:
      0 0 0 2px var(--color-blue-500),
      0 0 0 4px var(--color-blue-200);
  }

  /* 减少动画（用户偏好） */
  @media (prefers-reduced-motion: reduce) {
    .tab-transition,
    .window-control-button {
      transition: none;
    }
  }
}

/* 动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 工具类动画 */
.animate-fade-in {
  animation: fadeIn 0.2s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}
```

### 5. 类型定义（使用 Zod）

```typescript
// src/renderer/src/types/titlebar.ts
import { z } from 'zod'

export const TabSchema = z.object({
  id: z.string(),
  title: z.string(),
  active: z.boolean().default(false),
  isDirty: z.boolean().default(false),
  isPinned: z.boolean().default(false),
  icon: z.string().optional(),
  closable: z.boolean().default(true),
  tooltip: z.string().optional(),
  lastModified: z.date().optional(),
  metadata: z.record(z.any()).optional()
})

export const WindowStateSchema = z.object({
  isMaximized: z.boolean(),
  isMinimized: z.boolean(),
  isFullscreen: z.boolean(),
  isFocused: z.boolean()
})

export const TitlebarConfigSchema = z.object({
  showIcon: z.boolean().default(true),
  showTitle: z.boolean().default(true),
  maxVisibleTabs: z.number().min(1).max(50).default(8),
  enableTabReordering: z.boolean().default(true),
  enableTabContextMenu: z.boolean().default(true),
  enableVirtualization: z.boolean().default(true),
  customStyles: z.record(z.string()).optional()
})

export const WindowControlActionSchema = z.enum([
  'minimize',
  'maximize',
  'restore',
  'close',
  'fullscreen'
])

export const TabActionSchema = z.enum([
  'activate',
  'close',
  'pin',
  'unpin',
  'duplicate',
  'move',
  'refresh'
])

export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  timestamp: z.number().default(() => Date.now())
})

// 导出推断类型
export type Tab = z.infer<typeof TabSchema>
export type WindowState = z.infer<typeof WindowStateSchema>
export type TitlebarConfig = z.infer<typeof TitlebarConfigSchema>
export type WindowControlAction = z.infer<typeof WindowControlActionSchema>
export type TabAction = z.infer<typeof TabActionSchema>

export type ApiResponse<T = any> = z.infer<typeof ApiResponseSchema> & {
  data?: T
}
```

### 6. 主进程

```typescript
// src/main/index.ts
import { app, shell, BrowserWindow } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import { setupWindowControls } from './ipc/window-controls'
import icon from '../../resources/icon.png?asset'

function createWindow(): void {
  const mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 800,
    minHeight: 600,
    show: false,
    frame: false, // 隐藏原生标题栏
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden',
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // 设置窗口控制 IPC
  setupWindowControls(mainWindow)

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // 开发环境
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }

  // 窗口状态变化通知
  mainWindow.on('maximize', () => {
    mainWindow.webContents.send('window:state-changed', { isMaximized: true })
  })

  mainWindow.on('unmaximize', () => {
    mainWindow.webContents.send('window:state-changed', { isMaximized: false })
  })

  mainWindow.on('focus', () => {
    mainWindow.webContents.send('window:state-changed', { isFocused: true })
  })

  mainWindow.on('blur', () => {
    mainWindow.webContents.send('window:state-changed', { isFocused: false })
  })
}

app.whenReady().then(() => {
  electronApp.setAppUserModelId('com.electron')

  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  createWindow()

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit()
})
```

### 7. IPC 窗口控制

```typescript
// src/main/ipc/window-controls.ts
import { BrowserWindow, ipcMain, Menu, dialog } from 'electron'
import type { ApiResponse } from '../../renderer/src/types/titlebar'

export function setupWindowControls(mainWindow: BrowserWindow): void {
  // 窗口最小化
  ipcMain.handle('window:minimize', async (): Promise<ApiResponse> => {
    try {
      mainWindow.minimize()
      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  })

  // 窗口最大化/还原
  ipcMain.handle('window:maximize', async (): Promise<ApiResponse<{ isMaximized: boolean }>> => {
    try {
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize()
        return { success: true, data: { isMaximized: false } }
      } else {
        mainWindow.maximize()
        return { success: true, data: { isMaximized: true } }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  })

  // 窗口关闭
  ipcMain.handle('window:close', async (): Promise<ApiResponse> => {
    try {
      // 检查是否有未保存的更改
      const hasUnsavedChanges = await mainWindow.webContents.executeJavaScript(`
        window.electronAPI?.hasUnsavedChanges?.() || false
      `)

      if (hasUnsavedChanges) {
        const result = await dialog.showMessageBox(mainWindow, {
          type: 'warning',
          buttons: ['保存并关闭', '不保存关闭', '取消'],
          defaultId: 0,
          cancelId: 2,
          title: '确认关闭',
          message: '您有未保存的更改，是否要保存？'
        })

        switch (result.response) {
          case 0: // 保存并关闭
            await mainWindow.webContents.executeJavaScript(`
              window.electronAPI?.saveAll?.()
            `)
            mainWindow.close()
            break
          case 1: // 不保存关闭
            mainWindow.close()
            break
          case 2: // 取消
            return { success: false, error: 'User cancelled' }
        }
      } else {
        mainWindow.close()
      }

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  })

  // 获取窗口状态
  ipcMain.handle('window:get-state', async () => {
    try {
      return {
        success: true,
        data: {
          isMaximized: mainWindow.isMaximized(),
          isMinimized: mainWindow.isMinimized(),
          isFullscreen: mainWindow.isFullScreen(),
          isFocused: mainWindow.isFocused()
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  })

  // 设置窗口标题
  ipcMain.handle('window:set-title', async (_, title: string) => {
    try {
      mainWindow.setTitle(title)
      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  })

  // 显示标题栏右键菜单
  ipcMain.handle('titlebar:show-context-menu', async (_, x: number, y: number) => {
    try {
      const template = [
        {
          label: mainWindow.isMaximized() ? '还原' : '最大化',
          click: () => {
            if (mainWindow.isMaximized()) {
              mainWindow.unmaximize()
            } else {
              mainWindow.maximize()
            }
          }
        },
        {
          label: '最小化',
          click: () => mainWindow.minimize()
        },
        { type: 'separator' as const },
        {
          label: '关闭',
          click: () => mainWindow.close()
        }
      ]

      const menu = Menu.buildFromTemplate(template)
      menu.popup({
        window: mainWindow,
        x: Math.round(x),
        y: Math.round(y)
      })

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  })
}
```

### 8. 预加载脚本

```typescript
// src/preload/index.ts
import { contextBridge, ipcRenderer } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import type { WindowState, ApiResponse } from '../renderer/src/types/titlebar'

// 自定义 APIs
const api = {
  // 窗口控制
  window: {
    minimize: (): Promise<ApiResponse> => ipcRenderer.invoke('window:minimize'),

    maximize: (): Promise<ApiResponse<{ isMaximized: boolean }>> =>
      ipcRenderer.invoke('window:maximize'),

    close: (): Promise<ApiResponse> => ipcRenderer.invoke('window:close'),

    getState: (): Promise<ApiResponse<WindowState>> => ipcRenderer.invoke('window:get-state'),

    setTitle: (title: string): Promise<ApiResponse> =>
      ipcRenderer.invoke('window:set-title', title),

    onStateChanged: (callback: (state: Partial<WindowState>) => void) => {
      const handler = (_: any, state: Partial<WindowState>) => callback(state)
      ipcRenderer.on('window:state-changed', handler)
      return () => ipcRenderer.removeListener('window:state-changed', handler)
    }
  },

  // 标题栏功能
  titlebar: {
    showContextMenu: (x: number, y: number): Promise<ApiResponse> =>
      ipcRenderer.invoke('titlebar:show-context-menu', x, y)
  },

  // 系统信息
  system: {
    getPlatform: () => process.platform,
    getVersions: () => process.versions
  }
}

// 暴露到渲染进程
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}
```

### 9. 虚拟化标签页组件

```typescript
// src/renderer/src/components/titlebar/VirtualizedTabList.tsx
import React, { useMemo, useCallback, useRef, useEffect } from 'react'
import { FixedSizeList as List, areEqual } from 'react-window'
import { useDrag, useDrop, DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { TabItem } from './TabItem'
import { useTabManager } from '@/hooks/useTabManager'
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor'
import type { Tab, TitlebarConfig } from '@/types/titlebar'

interface VirtualizedTabListProps {
  tabs: Tab[]
  config: TitlebarConfig
  onTabAction: (action: string, tabId: string, payload?: any) => void
  className?: string
}

interface TabItemData {
  tabs: Tab[]
  onTabAction: (action: string, tabId: string, payload?: any) => void
  config: TitlebarConfig
}

const TAB_WIDTH = 200
const TAB_MIN_WIDTH = 120

// 虚拟化列表项组件
const VirtualTabItem = React.memo(({ index, style, data }: {
  index: number
  style: React.CSSProperties
  data: TabItemData
}) => {
  const { tabs, onTabAction, config } = data
  const tab = tabs[index]

  if (!tab) return null

  return (
    <div style={style}>
      <TabItem
        tab={tab}
        onAction={onTabAction}
        config={config}
        width={TAB_WIDTH}
      />
    </div>
  )
}, areEqual)

VirtualTabItem.displayName = 'VirtualTabItem'

export function VirtualizedTabList({
  tabs,
  config,
  onTabAction,
  className
}: VirtualizedTabListProps) {
  const listRef = useRef<List>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const { startTiming, endTiming } = usePerformanceMonitor('tab-list-render')

  const {
    visibleRange,
    canScrollLeft,
    canScrollRight,
    scrollLeft,
    scrollRight,
    activeTabIndex
  } = useTabManager(tabs, config.maxVisibleTabs)

  // 性能监控
  useEffect(() => {
    startTiming()
    const cleanup = () => endTiming()

    const id = requestIdleCallback ?
      requestIdleCallback(cleanup) :
      setTimeout(cleanup, 0)

    return () => {
      if (requestIdleCallback) {
        cancelIdleCallback(id as number)
      } else {
        clearTimeout(id as number)
      }
    }
  }, [tabs, startTiming, endTiming])

  // 确保活跃标签页可见
  useEffect(() => {
    if (activeTabIndex >= 0 && listRef.current) {
      const { start, end } = visibleRange

      if (activeTabIndex < start || activeTabIndex >= end) {
        listRef.current.scrollToItem(activeTabIndex, 'center')
      }
    }
  }, [activeTabIndex, visibleRange])

  // 计算可见标签页
  const visibleTabs = useMemo(() => {
    if (!config.enableVirtualization) return tabs

    const { start, end } = visibleRange
    return tabs.slice(start, end)
  }, [tabs, visibleRange, config.enableVirtualization])

  // 计算容器宽度
  const containerWidth = useMemo(() => {
    if (!containerRef.current) return 0
    return containerRef.current.offsetWidth - 80 // 减去滚动按钮宽度
  }, [])

  // 标签页项数据
  const itemData: TabItemData = useMemo(() => ({
    tabs: visibleTabs,
    onTabAction,
    config
  }), [visibleTabs, onTabAction, config])

  const handleScroll = useCallback((direction: 'left' | 'right') => {
    startTiming()
    if (direction === 'left') {
      scrollLeft()
    } else {
      scrollRight()
    }
    endTiming()
  }, [scrollLeft, scrollRight, startTiming, endTiming])

  if (!config.enableVirtualization) {
    // 非虚拟化模式，直接渲染所有标签页
    return (
      <div className={cn('flex items-center h-full overflow-hidden', className)}>
        <div className="flex-1 flex overflow-x-auto scrollbar-hide">
          {tabs.map((tab) => (
            <TabItem
              key={tab.id}
              tab={tab}
              onAction={onTabAction}
              config={config}
              width={TAB_WIDTH}
            />
          ))}
        </div>
      </div>
    )
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div
        ref={containerRef}
        className={cn('flex items-center h-full', className)}
      >
        {/* 左滚动按钮 */}
        {canScrollLeft && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleScroll('left')}
            className="h-8 w-8 p-0 shrink-0 mx-1 hover:bg-gray-100"
            aria-label="向左滚动标签页"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        )}

        {/* 虚拟化标签页列表 */}
        <div className="flex-1 h-full">
          <List
            ref={listRef}
            height={48}
            width={containerWidth}
            itemCount={visibleTabs.length}
            itemSize={TAB_WIDTH}
            itemData={itemData}
            layout="horizontal"
            overscanCount={2}
            className="scrollbar-hide"
          >
            {VirtualTabItem}
          </List>
        </div>

        {/* 右滚动按钮 */}
        {canScrollRight && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleScroll('right')}
            className="h-8 w-8 p-0 shrink-0 mx-1 hover:bg-gray-100"
            aria-label="向右滚动标签页"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}
      </div>
    </DndProvider>
  )
}
```

### 10. 标签页项组件

```typescript
// src/renderer/src/components/titlebar/TabItem.tsx
import React, { useCallback, useRef, useState } from 'react'
import { useDrag, useDrop } from 'react-dnd'
import { X, Pin, MoreHorizontal } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from '@/components/ui/context-menu'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'
import type { Tab, TitlebarConfig } from '@/types/titlebar'

interface TabItemProps {
  tab: Tab
  onAction: (action: string, tabId: string, payload?: any) => void
  config: TitlebarConfig
  width?: number
}

interface DragItem {
  type: string
  id: string
  index: number
}

const TAB_TYPE = 'TAB'

export function TabItem({
  tab,
  onAction,
  config,
  width = 200
}: TabItemProps) {
  const ref = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  // 拖拽功能
  const [{ isDragging: dragIsDragging }, drag] = useDrag({
    type: TAB_TYPE,
    item: () => ({ type: TAB_TYPE, id: tab.id, index: 0 }),
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    canDrag: () => config.enableTabReordering && !tab.isPinned,
    begin: () => setIsDragging(true),
    end: () => setIsDragging(false),
  })

  const [{ isOver }, drop] = useDrop({
    accept: TAB_TYPE,
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
    hover: (item: DragItem) => {
      if (item.id !== tab.id) {
        onAction('reorder', item.id, { targetId: tab.id })
      }
    },
  })

  // 组合 drag 和 drop refs
  drag(drop(ref))

  // 事件处理
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    onAction('activate', tab.id)
  }, [onAction, tab.id])

  const handleClose = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    onAction('close', tab.id)
  }, [onAction, tab.id])

  const handleMiddleClick = useCallback((e: React.MouseEvent) => {
    if (e.button === 1) { // 中键
      e.preventDefault()
      if (tab.closable) {
        onAction('close', tab.id)
      }
    }
  }, [onAction, tab.id, tab.closable])

  const handlePin = useCallback(() => {
    onAction(tab.isPinned ? 'unpin' : 'pin', tab.id)
  }, [onAction, tab.id, tab.isPinned])

  const handleDuplicate = useCallback(() => {
    onAction('duplicate', tab.id)
  }, [onAction, tab.id])

  // 渲染内容
  const tabContent = (
    <div
      ref={ref}
      className={cn(
        'relative flex items-center gap-2 px-3 py-2 cursor-pointer group',
        'transition-all duration-150 ease-out',
        'hover:bg-gray-50',
        'border-r border-gray-200',
        tab.active && [
          'bg-white',
          'border-b-2 border-blue-500',
          'shadow-sm'
        ],
        tab.isPinned && 'bg-blue-50',
        isDragging && 'opacity-50 scale-95',
        isOver && 'bg-blue-100',
        'max-w-[200px] min-w-[120px]'
      )}
      style={{ width }}
      onClick={handleClick}
      onMouseDown={handleMiddleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      title={tab.tooltip || tab.title}
    >
      {/* 图标 */}
      {tab.icon && (
        <img
          src={tab.icon}
          alt=""
          className="w-4 h-4 shrink-0"
        />
      )}

      {/* 固定图标 */}
      {tab.isPinned && (
        <Pin className="w-3 h-3 text-blue-500 shrink-0" />
      )}

      {/* 标题 */}
      <span className={cn(
        'flex-1 truncate text-sm',
        tab.active ? 'text-gray-900' : 'text-gray-600'
      )}>
        {tab.title}
      </span>

      {/* 未保存指示器 */}
      {tab.isDirty && (
        <div className="w-2 h-2 bg-orange-400 rounded-full shrink-0" />
      )}

      {/* 关闭按钮 */}
      {tab.closable && !tab.isPinned && (isHovered || tab.active) && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className={cn(
            'h-5 w-5 p-0 shrink-0 opacity-70 hover:opacity-100',
            'hover:bg-gray-200',
            'transition-opacity duration-150'
          )}
          aria-label={`关闭 ${tab.title}`}
        >
          <X className="h-3 w-3" />
        </Button>
      )}

      {/* 更多选项按钮 */}
      {config.enableTabContextMenu && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                'h-5 w-5 p-0 shrink-0 opacity-0 group-hover:opacity-70 hover:opacity-100',
                'hover:bg-gray-200',
                'transition-opacity duration-150'
              )}
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizontal className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handlePin}>
              {tab.isPinned ? '取消固定' : '固定标签页'}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDuplicate}>
              复制标签页
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={handleClose}
              disabled={!tab.closable}
              className="text-red-600"
            >
              关闭标签页
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onAction('close-others', tab.id)}
            >
              关闭其他标签页
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  )

  // 如果启用了右键菜单，包装 ContextMenu
  if (config.enableTabContextMenu) {
    return (
      <ContextMenu>
        <ContextMenuTrigger>
          {tabContent}
        </ContextMenuTrigger>
        <ContextMenuContent>
          <ContextMenuItem onClick={() => onAction('activate', tab.id)}>
            激活标签页
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuItem onClick={handlePin}>
            {tab.isPinned ? '取消固定' : '固定标签页'}
          </ContextMenuItem>
          <ContextMenuItem onClick={handleDuplicate}>
            复制标签页
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuItem
            onClick={handleClose}
            disabled={!tab.closable}
            className="text-red-600"
          >
            关闭标签页
          </ContextMenuItem>
          <ContextMenuItem onClick={() => onAction('close-others', tab.id)}>
            关闭其他标签页
          </ContextMenuItem>
          <ContextMenuItem onClick={() => onAction('close-to-right', tab.id)}>
            关闭右侧标签页
          </ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>
    )
  }

  return tabContent
}
```

### 11. 窗口控制按钮组件

```typescript
// src/renderer/src/components/titlebar/WindowControls.tsx
import React, { useState, useEffect, useCallback } from 'react'
import { Minus, Square, Copy, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { useWindowControls } from '@/hooks/useWindowControls'
import type { WindowState } from '@/types/titlebar'

interface WindowControlsProps {
  className?: string
  showOnHover?: boolean
}

export function WindowControls({
  className,
  showOnHover = false
}: WindowControlsProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [windowState, setWindowState] = useState<WindowState>({
    isMaximized: false,
    isMinimized: false,
    isFullscreen: false,
    isFocused: true
  })

  const {
    minimize,
    maximize,
    close,
    getState,
    isLoading
  } = useWindowControls()

  // 获取初始窗口状态
  useEffect(() => {
    getState().then(response => {
      if (response.success && response.data) {
        setWindowState(response.data)
      }
    })
  }, [getState])

  // 监听窗口状态变化
  useEffect(() => {
    const removeListener = window.api?.window.onStateChanged(
      (state: Partial<WindowState>) => {
        setWindowState(prev => ({ ...prev, ...state }))
      }
    )

    return removeListener
  }, [])

  const handleMinimize = useCallback(async () => {
    const response = await minimize()
    if (!response.success) {
      console.error('最小化失败:', response.error)
    }
  }, [minimize])

  const handleMaximize = useCallback(async () => {
    const response = await maximize()
    if (response.success && response.data) {
      setWindowState(prev => ({
        ...prev,
        isMaximized: response.data!.isMaximized
      }))
    } else if (!response.success) {
      console.error('最大化失败:', response.error)
    }
  }, [maximize])

  const handleClose = useCallback(async () => {
    const response = await close()
    if (!response.success && response.error !== 'User cancelled') {
      console.error('关闭失败:', response.error)
    }
  }, [close])

  // 根据平台调整样式
  const platform = window.api?.system.getPlatform()
  const isMacOS = platform === 'darwin'
  const isWindows = platform === 'win32'

  // macOS 通常使用系统的交通灯按钮
  if (isMacOS) {
    return null
  }

  const controlsVisible = !showOnHover || isHovered

  return (
    <div
      className={cn(
        'flex items-center h-full titlebar-no-drag',
        showOnHover && 'transition-opacity duration-200',
        showOnHover && !controlsVisible && 'opacity-0',
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 最小化按钮 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleMinimize}
        disabled={isLoading}
        className={cn(
          'window-control-button h-12 rounded-none transition-colors duration-150',
          isWindows ? 'w-12' : 'w-10',
          'hover:bg-gray-100',
          'focus:bg-gray-200'
        )}
        aria-label="最小化窗口"
      >
        <Minus className="h-4 w-4" />
      </Button>

      {/* 最大化/还原按钮 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleMaximize}
        disabled={isLoading}
        className={cn(
          'window-control-button h-12 rounded-none transition-colors duration-150',
          isWindows ? 'w-12' : 'w-10',
          'hover:bg-gray-100',
          'focus:bg-gray-200'
        )}
        aria-label={windowState.isMaximized ? '还原窗口' : '最大化窗口'}
      >
        {windowState.isMaximized ? (
          <Copy className="h-4 w-4" />
        ) : (
          <Square className="h-4 w-4" />
        )}
      </Button>

      {/* 关闭按钮 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleClose}
        disabled={isLoading}
        className={cn(
          'window-control-button h-12 rounded-none transition-colors duration-150',
          isWindows ? 'w-12' : 'w-10',
          'hover:bg-red-500 hover:text-white',
          'focus:bg-red-600 focus:text-white'
        )}
        aria-label="关闭窗口"
      >
        <X className="h-4 w-4" />
      </Button>
    </div>
  )
}
```

### 12. 主标题栏组件

```typescript
// src/renderer/src/components/titlebar/CustomTitlebar.tsx
import React, { useCallback } from 'react'
import { Settings, MoreHorizontal, ArrowLeft, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { VirtualizedTabList } from './VirtualizedTabList'
import { WindowControls } from './WindowControls'
import { useTitlebarStore } from '@/stores/titlebar-store'
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor'

interface CustomTitlebarProps {
  title?: string
  className?: string
  children?: React.ReactNode
}

export function CustomTitlebar({
  title = "作家助手",
  className,
  children
}: CustomTitlebarProps) {
  const {
    tabs,
    config,
    updateTab,
    removeTab,
    reorderTabs,
    duplicateTab,
    closeOtherTabs,
    closeTabsToRight,
    pinTab,
    unpinTab
  } = useTitlebarStore()

  const { startTiming, endTiming } = usePerformanceMonitor('titlebar-render')
  const platform = window.api?.system.getPlatform()

  // 性能监控
  React.useEffect(() => {
    startTiming()
    return () => endTiming()
  }, [tabs, startTiming, endTiming])

  // 处理标签页操作
  const handleTabAction = useCallback((action: string, tabId: string, payload?: any) => {
    switch (action) {
      case 'activate':
        updateTab(tabId, { active: true })
        // 设置窗口标题
        const tab = tabs.find(t => t.id === tabId)
        if (tab) {
          window.api?.window.setTitle(`${tab.title} - ${title}`)
        }
        break

      case 'close':
        removeTab(tabId)
        break

      case 'pin':
        pinTab(tabId)
        break

      case 'unpin':
        unpinTab(tabId)
        break

      case 'duplicate':
        duplicateTab(tabId)
        break

      case 'reorder':
        if (payload?.targetId) {
          reorderTabs(tabId, payload.targetId)
        }
        break

      case 'close-others':
        closeOtherTabs(tabId)
        break

      case 'close-to-right':
        closeTabsToRight(tabId)
        break

      default:
        console.warn('未知的标签页操作:', action)
    }
  }, [
    tabs,
    title,
    updateTab,
    removeTab,
    reorderTabs,
    duplicateTab,
    closeOtherTabs,
    closeTabsToRight,
    pinTab,
    unpinTab
  ])

  // 处理标题栏右键菜单
  const handleTitlebarContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    window.api?.titlebar.showContextMenu(e.clientX, e.clientY)
  }, [])

  // macOS 特殊处理
  const isMacOS = platform === 'darwin'
  const titlebarHeight = isMacOS ? 28 : 32

  return (
    <div
      className={cn(
        'flex items-center bg-white border-b border-gray-200',
        'select-none relative z-50 titlebar-drag-region',
        className
      )}
      style={{ height: titlebarHeight }}
      onContextMenu={handleTitlebarContextMenu}
    >
      {/* 左侧：应用图标和标题 */}
      <div
        className={cn(
          'flex items-center gap-3 px-4 shrink-0 titlebar-no-drag',
          isMacOS && 'pl-20' // 为 macOS 交通灯按钮留空间
        )}
      >
        {config.showIcon && (
          <div className="w-5 h-5 bg-gradient-to-br from-blue-500 to-blue-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">作</span>
          </div>
        )}

        {config.showTitle && (
          <span className="text-sm font-medium text-gray-700">
            {title}
          </span>
        )}
      </div>

      {tabs.length > 0 && (
        <>
          <Separator orientation="vertical" className="h-6" />

          {/* 标签页区域 */}
          <div className="flex-1 min-w-0 h-full">
            <VirtualizedTabList
              tabs={tabs}
              config={config}
              onTabAction={handleTabAction}
              className="h-full"
            />
          </div>
        </>
      )}

      {/* 中间：可拖拽区域 */}
      <div className="flex-1 h-full min-w-[100px] titlebar-drag-region" />

      {/* 右侧：工具按钮和窗口控制 */}
      <div className="flex items-center h-full shrink-0 titlebar-no-drag">
        {/* 工具按钮 */}
        <div className="flex items-center gap-1 px-2">
          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-7 p-0 hover:bg-gray-100"
            aria-label="后退"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-7 p-0 hover:bg-gray-100"
            aria-label="刷新"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>

          <Separator orientation="vertical" className="h-4 mx-1" />

          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-7 p-0 hover:bg-gray-100"
            aria-label="设置"
          >
            <Settings className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-7 p-0 hover:bg-gray-100"
            aria-label="更多选项"
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>

        {!isMacOS && (
          <>
            <Separator orientation="vertical" className="h-6" />
            <WindowControls />
          </>
        )}
      </div>

      {/* 自定义内容 */}
      {children}
    </div>
  )
}
```

### 13. Hooks

```typescript
// src/renderer/src/hooks/useWindowControls.ts
import { useState, useCallback } from 'react'
import type { ApiResponse, WindowState } from '@/types/titlebar'

export function useWindowControls() {
  const [isLoading, setIsLoading] = useState(false)

  const minimize = useCallback(async (): Promise<ApiResponse> => {
    setIsLoading(true)
    try {
      const response = await window.api.window.minimize()
      return response
    } finally {
      setIsLoading(false)
    }
  }, [])

  const maximize = useCallback(async (): Promise<ApiResponse<{ isMaximized: boolean }>> => {
    setIsLoading(true)
    try {
      const response = await window.api.window.maximize()
      return response
    } finally {
      setIsLoading(false)
    }
  }, [])

  const close = useCallback(async (): Promise<ApiResponse> => {
    setIsLoading(true)
    try {
      const response = await window.api.window.close()
      return response
    } finally {
      setIsLoading(false)
    }
  }, [])

  const getState = useCallback(async (): Promise<ApiResponse<WindowState>> => {
    return await window.api.window.getState()
  }, [])

  return {
    minimize,
    maximize,
    close,
    getState,
    isLoading
  }
}
```

```typescript
// src/renderer/src/hooks/useTabManager.ts
import { useMemo, useState, useCallback } from 'react'
import type { Tab } from '@/types/titlebar'

export function useTabManager(tabs: Tab[], maxVisibleTabs: number) {
  const [scrollOffset, setScrollOffset] = useState(0)

  const activeTabIndex = useMemo(() => tabs.findIndex((tab) => tab.active), [tabs])

  const visibleRange = useMemo(() => {
    if (tabs.length <= maxVisibleTabs) {
      return { start: 0, end: tabs.length }
    }

    let start = scrollOffset
    let end = start + maxVisibleTabs

    // 确保活跃标签页可见
    if (activeTabIndex >= 0) {
      if (activeTabIndex < start) {
        start = activeTabIndex
        end = start + maxVisibleTabs
      } else if (activeTabIndex >= end) {
        end = activeTabIndex + 1
        start = end - maxVisibleTabs
      }
    }

    // 边界检查
    start = Math.max(0, start)
    end = Math.min(tabs.length, end)

    if (end - start < maxVisibleTabs) {
      start = Math.max(0, end - maxVisibleTabs)
    }

    return { start, end }
  }, [tabs.length, maxVisibleTabs, scrollOffset, activeTabIndex])

  const canScrollLeft = visibleRange.start > 0
  const canScrollRight = visibleRange.end < tabs.length

  const scrollLeft = useCallback(() => {
    setScrollOffset(Math.max(0, scrollOffset - 1))
  }, [scrollOffset])

  const scrollRight = useCallback(() => {
    setScrollOffset(Math.min(tabs.length - maxVisibleTabs, scrollOffset + 1))
  }, [scrollOffset, tabs.length, maxVisibleTabs])

  return {
    visibleRange,
    activeTabIndex,
    canScrollLeft,
    canScrollRight,
    scrollLeft,
    scrollRight
  }
}
```

```typescript
// src/renderer/src/hooks/usePerformanceMonitor.ts
import { useCallback, useRef } from 'react'
import type { PerformanceMetric } from '@/types/titlebar'

class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics = new Map<string, PerformanceMetric[]>()

  static getInstance() {
    if (!this.instance) {
      this.instance = new PerformanceMonitor()
    }
    return this.instance
  }

  startTiming(name: string) {
    performance.mark(`${name}-start`)
  }

  endTiming(name: string) {
    performance.mark(`${name}-end`)
    performance.measure(name, `${name}-start`, `${name}-end`)

    const measure = performance.getEntriesByName(name).pop() as PerformanceMeasure
    if (measure) {
      const metric: PerformanceMetric = {
        name,
        duration: measure.duration,
        timestamp: Date.now(),
        memory: (performance as any).memory?.usedJSHeapSize
      }

      const metrics = this.metrics.get(name) || []
      metrics.push(metric)

      // 只保留最近 50 条记录
      if (metrics.length > 50) {
        metrics.shift()
      }

      this.metrics.set(name, metrics)

      // 性能警告
      if (measure.duration > 16.67) {
        // 超过一帧的时间
        console.warn(`性能警告: ${name} 耗时 ${measure.duration.toFixed(2)}ms`)
      }
    }
  }

  getMetrics(name: string): PerformanceMetric[] {
    return this.metrics.get(name) || []
  }

  getAverageTime(name: string): number {
    const metrics = this.getMetrics(name)
    if (metrics.length === 0) return 0

    const total = metrics.reduce((sum, metric) => sum + metric.duration, 0)
    return total / metrics.length
  }
}

export function usePerformanceMonitor(name: string) {
  const monitor = useRef(PerformanceMonitor.getInstance())

  const startTiming = useCallback(() => {
    monitor.current.startTiming(name)
  }, [name])

  const endTiming = useCallback(() => {
    monitor.current.endTiming(name)
  }, [name])

  const getMetrics = useCallback(() => {
    return monitor.current.getMetrics(name)
  }, [name])

  const getAverageTime = useCallback(() => {
    return monitor.current.getAverageTime(name)
  }, [name])

  return {
    startTiming,
    endTiming,
    getMetrics,
    getAverageTime
  }
}
```

### 14. 状态管理

```typescript
// src/renderer/src/stores/titlebar-store.ts
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import {
  TabSchema,
  TitlebarConfigSchema,
  type Tab,
  type TitlebarConfig
} from '@/types/titlebar'

interface TitlebarState {
  tabs: Tab[]
  config: TitlebarConfig

  // Actions
  addTab: (tab: Omit<Tab, 'active'>) => void
  removeTab: (id: string) => void
  updateTab: (id: string, updates: Partial<Tab>) => void
  reorderTabs: (fromId: string, toId: string) => void
  duplicateTab: (id: string) => void
  closeOtherTabs: (id: string) => void
  closeTabsToRight: (id: string) => void
  pinTab: (id: string) => void
  unpinTab: (id: string) => void
  updateConfig: (config: Partial<TitlebarConfig>) => void
}

export const useTitlebarStore = create<TitlebarState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      tabs: [],
      config: TitlebarConfigSchema.parse({}),

      addTab: (tabData) => {
        const tab = TabSchema.parse(tabData)

        set((state) => {
          // 取消其他标签页的激活状态
          state.tabs.forEach(t => t.active = false)

          // 添加新标签页
          state.tabs.push({ ...tab, active: true })
        })
      },

      removeTab: (id) => {
        set((state) => {
          const index = state.tabs.findIndex(tab => tab.id === id)
          if (index === -1) return

          const wasActive = state.tabs[index].active
          state.tabs.splice(index, 1)

          //
```

```typescript
// src/renderer/src/stores/titlebar-store.ts (续)
      removeTab: (id) => {
        set((state) => {
          const index = state.tabs.findIndex(tab => tab.id === id)
          if (index === -1) return

          const wasActive = state.tabs[index].active
          state.tabs.splice(index, 1)

          // 如果删除的是活跃标签页，激活相邻的标签页
          if (wasActive && state.tabs.length > 0) {
            const nextIndex = Math.min(index, state.tabs.length - 1)
            state.tabs[nextIndex].active = true
          }
        })
      },

      updateTab: (id, updates) => {
        set((state) => {
          const tab = state.tabs.find(t => t.id === id)
          if (!tab) return

          // 如果要激活这个标签页，先取消其他标签页的激活状态
          if (updates.active === true) {
            state.tabs.forEach(t => t.active = false)
          }

          Object.assign(tab, updates)
        })
      },

      reorderTabs: (fromId, toId) => {
        set((state) => {
          const fromIndex = state.tabs.findIndex(tab => tab.id === fromId)
          const toIndex = state.tabs.findIndex(tab => tab.id === toId)

          if (fromIndex === -1 || toIndex === -1) return

          const [movedTab] = state.tabs.splice(fromIndex, 1)
          state.tabs.splice(toIndex, 0, movedTab)
        })
      },

      duplicateTab: (id) => {
        set((state) => {
          const original = state.tabs.find(tab => tab.id === id)
          if (!original) return

          const duplicate: Tab = {
            ...original,
            id: `${original.id}-copy-${Date.now()}`,
            title: `${original.title} (副本)`,
            active: false,
            isDirty: false
          }

          const index = state.tabs.findIndex(tab => tab.id === id)
          state.tabs.splice(index + 1, 0, duplicate)
        })
      },

      closeOtherTabs: (id) => {
        set((state) => {
          const keepTab = state.tabs.find(tab => tab.id === id)
          if (!keepTab) return

          state.tabs = [{ ...keepTab, active: true }]
        })
      },

      closeTabsToRight: (id) => {
        set((state) => {
          const index = state.tabs.findIndex(tab => tab.id === id)
          if (index === -1) return

          state.tabs = state.tabs.slice(0, index + 1)
        })
      },

      pinTab: (id) => {
        set((state) => {
          const tab = state.tabs.find(t => t.id === id)
          if (tab) {
            tab.isPinned = true
            tab.closable = false

            // 将固定的标签页移到前面
            const index = state.tabs.findIndex(t => t.id === id)
            const [pinnedTab] = state.tabs.splice(index, 1)

            // 找到第一个非固定标签页的位置
            const insertIndex = state.tabs.findIndex(t => !t.isPinned)
            state.tabs.splice(insertIndex === -1 ? state.tabs.length : insertIndex, 0, pinnedTab)
          }
        })
      },

      unpinTab: (id) => {
        set((state) => {
          const tab = state.tabs.find(t => t.id === id)
          if (tab) {
            tab.isPinned = false
            tab.closable = true
          }
        })
      },

      updateConfig: (configUpdates) => {
        set((state) => {
          state.config = { ...state.config, ...configUpdates }
        })
      }
    }))
  )
)

// 订阅标签页变化，自动保存到本地存储
useTitlebarStore.subscribe(
  (state) => state.tabs,
  (tabs) => {
    localStorage.setItem('titlebar-tabs', JSON.stringify(tabs))
  }
)

// 初始化时从本地存储恢复标签页
const savedTabs = localStorage.getItem('titlebar-tabs')
if (savedTabs) {
  try {
    const tabs = JSON.parse(savedTabs)
    useTitlebarStore.setState({ tabs })
  } catch (error) {
    console.error('恢复标签页失败:', error)
  }
}
```

### 15. 主应用组件

```typescript
// src/renderer/src/App.tsx
import React, { useEffect } from 'react'
import { CustomTitlebar } from '@/components/titlebar/CustomTitlebar'
import { useTitlebarStore } from '@/stores/titlebar-store'
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor'
import { ErrorBoundary } from 'react-error-boundary'
import '@/globals.css'

function ErrorFallback({ error, resetErrorBoundary }: any) {
  return (
    <div className="h-screen flex items-center justify-center bg-red-50">
      <div className="text-center">
        <h2 className="text-lg font-semibold text-red-800 mb-2">
          应用出现错误
        </h2>
        <p className="text-red-600 mb-4">{error.message}</p>
        <button
          onClick={resetErrorBoundary}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          重新启动
        </button>
      </div>
    </div>
  )
}

export default function App() {
  const { addTab } = useTitlebarStore()
  const { startTiming, endTiming } = usePerformanceMonitor('app-render')

  // 初始化示例标签页
  useEffect(() => {
    startTiming()

    // 检查是否已有标签页，避免重复添加
    const { tabs } = useTitlebarStore.getState()
    if (tabs.length === 0) {
      addTab({
        id: '1',
        title: '三千繁夜',
        isDirty: true,
        icon: '/icons/document.svg'
      })

      addTab({
        id: '2',
        title: '无标题文档',
        isDirty: false
      })
    }

    return () => endTiming()
  }, [addTab, startTiming, endTiming])

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <div className="h-screen flex flex-col bg-gray-50">
        {/* 自定义标题栏 */}
        <CustomTitlebar title="作家助手" />

        {/* 主内容区域 */}
        <main className="flex-1 overflow-hidden">
          <div className="h-full p-6">
            <div className="bg-white rounded-lg shadow-sm h-full p-8">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">
                欢迎使用作家助手
              </h1>
              <p className="text-gray-600">
                这是一个带有自定义标题栏的 Electron 应用示例。
                您可以通过标签页在不同的文档之间切换。
              </p>

              {/* 示例内容区域 */}
              <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-blue-900 mb-2">
                    高性能标签页
                  </h3>
                  <p className="text-blue-700 text-sm">
                    使用虚拟化技术，支持大量标签页而不影响性能
                  </p>
                </div>

                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-green-900 mb-2">
                    跨平台兼容
                  </h3>
                  <p className="text-green-700 text-sm">
                    完美适配 Windows、macOS 和 Linux 三大平台
                  </p>
                </div>

                <div className="bg-purple-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-purple-900 mb-2">
                    类型安全
                  </h3>
                  <p className="text-purple-700 text-sm">
                    使用 Zod 进行运行时类型验证，确保应用稳定性
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </ErrorBoundary>
  )
}
```

### 16. 入口文件

```typescript
// src/renderer/src/main.tsx
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
)
```

### 17. 工具函数

```typescript
// src/renderer/src/lib/utils.ts
import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
```

### 18. 类型声明

```typescript
// src/renderer/src/types/electron.d.ts
import type { ElectronAPI } from '@electron-toolkit/preload'
import type { WindowState, ApiResponse } from './titlebar'

declare global {
  interface Window {
    electron: ElectronAPI
    api: {
      window: {
        minimize: () => Promise<ApiResponse>
        maximize: () => Promise<ApiResponse<{ isMaximized: boolean }>>
        close: () => Promise<ApiResponse>
        getState: () => Promise<ApiResponse<WindowState>>
        setTitle: (title: string) => Promise<ApiResponse>
        onStateChanged: (callback: (state: Partial<WindowState>) => void) => () => void
      }
      titlebar: {
        showContextMenu: (x: number, y: number) => Promise<ApiResponse>
      }
      system: {
        getPlatform: () => NodeJS.Platform
        getVersions: () => NodeJS.ProcessVersions
      }
    }
  }
}
```

### 19. package.json

```json
{
  "name": "electron-custom-titlebar",
  "version": "1.0.0",
  "description": "Electron app with custom titlebar using Tailwind CSS v4 and electron-vite",
  "main": "./out/main/index.js",
  "author": "Your Name",
  "homepage": "https://github.com/yourname/electron-custom-titlebar",
  "scripts": {
    "format": "prettier --write .",
    "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false",
    "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false",
    "typecheck": "npm run typecheck:node && npm run typecheck:web",
    "start": "electron-vite preview",
    "dev": "electron-vite dev",
    "build": "npm run typecheck && electron-vite build",
    "postinstall": "electron-builder install-app-deps",
    "build:win": "npm run build && electron-builder --win",
    "build:mac": "npm run build && electron-builder --mac",
    "build:linux": "npm run build && electron-builder --linux"
  },
  "dependencies": {
    "@electron-toolkit/preload": "^3.0.0",
    "@electron-toolkit/utils": "^3.0.0",
    "@radix-ui/react-context-menu": "^2.1.5",
    "@radix-ui/react-dropdown-menu": "^2.0.6",
    "@radix-ui/react-separator": "^1.0.3",
    "@radix-ui/react-tabs": "^1.0.4",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "lodash-es": "^4.17.21",
    "lucide-react": "^0.294.0",
    "react": "^18.2.0",
    "react-dnd": "^16.0.1",
    "react-dnd-html5-backend": "^16.0.1",
    "react-dom": "^18.2.0",
    "react-error-boundary": "^4.0.11",
    "react-window": "^1.8.8",
    "tailwind-merge": "^2.0.0",
    "zod": "^3.22.4",
    "zustand": "^4.4.7"
  },
  "devDependencies": {
    "@electron-toolkit/eslint-config-ts": "^1.0.1",
    "@electron-toolkit/tsconfig": "^1.0.1",
    "@tailwindcss/vite": "^4.0.0-alpha.15",
    "@types/lodash-es": "^4.17.12",
    "@types/node": "^20.9.0",
    "@types/react": "^18.2.37",
    "@types/react-dom": "^18.2.15",
    "@types/react-window": "^1.8.8",
    "@vitejs/plugin-react": "^4.1.1",
    "electron": "^27.1.2",
    "electron-builder": "^24.6.4",
    "electron-vite": "^2.0.0",
    "eslint": "^8.53.0",
    "eslint-plugin-react": "^7.33.2",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.4",
    "prettier": "^3.0.3",
    "tailwindcss": "^4.0.0-alpha.15",
    "typescript": "^5.2.2",
    "vite": "^5.0.0"
  }
}
```

### 20. 构建配置

```json
// electron.vite.config.ts 中的 build 配置
{
  "build": {
    "appId": "com.yourcompany.titlebar-app",
    "productName": "作家助手",
    "directories": {
      "output": "dist"
    },
    "files": ["out/**/*", "resources/**/*"],
    "mac": {
      "category": "public.app-category.productivity",
      "target": [
        {
          "target": "dmg",
          "arch": ["x64", "arm64"]
        }
      ]
    },
    "win": {
      "target": [
        {
          "target": "nsis",
          "arch": ["x64"]
        }
      ]
    },
    "linux": {
      "target": [
        {
          "target": "AppImage",
          "arch": ["x64"]
        }
      ]
    }
  }
}
```

### 21. 使用方法

```bash
# 开发模式
npm run dev

# 构建应用
npm run build

# 打包为可执行文件
npm run build:win    # Windows
npm run build:mac    # macOS
npm run build:linux  # Linux
```

## 总结

这个完整的解决方案提供了：

### ✅ 现代技术栈

- 🆕 **Tailwind CSS v4** - 使用最新的 CSS-first 配置
- ⚡ **electron-vite** - 最新的 Electron 开发框架
- 🎨 **shadcn/ui** - 现代化的组件库
- 🔍 **Zod** - 运行时类型验证

### 🚀 核心功能

- 🎯 **完全自定义标题栏** - 隐藏系统标题栏
- 📑 **虚拟化标签页** - 高性能渲染
- 🔄 **拖拽排序** - 标签页重新排序
- 📌 **标签页固定** - 重要标签页固定
- 🖱️ **上下文菜单** - 丰富的右键操作

### 🛡️ 性能与质量

- ⚡ **虚拟化渲染** - 只渲染可见元素
- 🧠 **智能预加载** - 性能监控
- 💾 **内存管理** - 自动清理
- 🔍 **类型安全** - Zod 运行时验证
- 🚫 **错误边界** - 优雅错误处理

### 🌍 跨平台支持

- 🪟 **Windows** - 完美适配
- 🍎 **macOS** - 支持交通灯按钮
- 🐧 **Linux** - 兼容各种桌面环境

这个方案已经过优化，去除了主题支持功能，专注于核心的标题栏功能，使用最新的技术栈，确保代码的现代性和可维护性。
