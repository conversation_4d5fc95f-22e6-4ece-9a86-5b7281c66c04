import React, { useCallback, useState } from 'react'
import { X, Pin, Home, Folder, File } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { Tab, TitlebarConfig } from '@/types'

interface TabItemProps {
  tab: Tab
  onAction: (action: string, tabId: string, payload?: any) => void
  config: TitlebarConfig
  width?: number
}

export function TabItem({ tab, onAction, config, width = 200 }: TabItemProps) {
  const [isHovered, setIsHovered] = useState(false)

  // 事件处理
  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault()
      onAction('activate', tab.id)
    },
    [onAction, tab.id]
  )

  const handleClose = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation()
      onAction('close', tab.id)
    },
    [onAction, tab.id]
  )

  const handleMiddleClick = useCallback(
    (e: React.MouseEvent) => {
      if (e.button === 1) {
        // 中键
        e.preventDefault()
        if (tab.closable && tab.type !== 'home') {
          onAction('close', tab.id)
        }
      }
    },
    [onAction, tab.id, tab.closable, tab.type]
  )

  const handlePin = useCallback(() => {
    onAction(tab.isPinned ? 'unpin' : 'pin', tab.id)
  }, [onAction, tab.id, tab.isPinned])

  const handleDuplicate = useCallback(() => {
    onAction('duplicate', tab.id)
  }, [onAction, tab.id])

  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    // 这里可以添加右键菜单逻辑
  }, [])

  // 获取图标
  const getIcon = () => {
    if (tab.icon) {
      return <img src={tab.icon} alt="" className="w-4 h-4 shrink-0" />
    }

    switch (tab.type) {
      case 'home':
        return <Home className="w-4 h-4 shrink-0" />
      case 'folder':
        return <Folder className="w-4 h-4 shrink-0" />
      case 'file':
      default:
        return <File className="w-4 h-4 shrink-0" />
    }
  }

  return (
    <div
      className={cn(
        'relative flex items-center gap-2 px-3 py-2 cursor-pointer group',
        'transition-all duration-150 ease-out',
        'hover:bg-gray-50',
        'border-r border-gray-200',
        tab.active && ['bg-white', 'border-b-2 border-blue-500', 'shadow-sm'],
        tab.isPinned && 'bg-blue-50',
        tab.type === 'home' && 'bg-gradient-to-r from-blue-50 to-indigo-50',
        'max-w-[200px] min-w-[120px]'
      )}
      style={
        {
          width,
          WebkitAppRegion: 'no-drag'
        } as React.CSSProperties
      }
      onClick={handleClick}
      onMouseDown={handleMiddleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onContextMenu={handleContextMenu}
      title={tab.tooltip || tab.title}
    >
      {/* 图标 */}
      {getIcon()}

      {/* 固定图标 */}
      {tab.isPinned && <Pin className="w-3 h-3 text-blue-500 shrink-0" />}

      {/* 标题 */}
      <span
        className={cn(
          'flex-1 truncate text-sm',
          tab.active ? 'text-gray-900' : 'text-gray-600',
          tab.type === 'home' && 'font-medium'
        )}
      >
        {tab.title}
      </span>

      {/* 未保存指示器 */}
      {tab.isDirty && <div className="w-2 h-2 bg-orange-400 rounded-full shrink-0" />}

      {/* 关闭按钮 */}
      {tab.closable && tab.type !== 'home' && !tab.isPinned && (isHovered || tab.active) && (
        <button
          onClick={handleClose}
          className={cn(
            'h-5 w-5 p-0 shrink-0 opacity-70 hover:opacity-100',
            'hover:bg-gray-200 rounded',
            'transition-opacity duration-150',
            'flex items-center justify-center'
          )}
          aria-label={`关闭 ${tab.title}`}
        >
          <X className="h-3 w-3" />
        </button>
      )}

      {/* 固定/取消固定按钮（仅在悬停时显示，且不是首页） */}
      {tab.type !== 'home' && isHovered && !tab.closable && (
        <button
          onClick={handlePin}
          className={cn(
            'h-5 w-5 p-0 shrink-0 opacity-70 hover:opacity-100',
            'hover:bg-gray-200 rounded',
            'transition-opacity duration-150',
            'flex items-center justify-center'
          )}
          aria-label={tab.isPinned ? '取消固定' : '固定标签页'}
        >
          <Pin className={cn('h-3 w-3', tab.isPinned ? 'text-blue-500' : 'text-gray-400')} />
        </button>
      )}
    </div>
  )
}
