{
  "extends": "@electron-toolkit/tsconfig/tsconfig.web.json",
  "include": [
    "src/renderer/src/env.d.ts",
    "src/renderer/src/**/*",
    "src/renderer/src/**/*.tsx",
    "src/preload/*.d.ts",
  ],
  "compilerOptions": {
    "composite": true,
    "jsx": "react-jsx",
    "skipLibCheck": true,
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "src/renderer/src/*"
      ],
      "@renderer/*": [
        "src/renderer/src/*"
      ]
    }
  }
}
