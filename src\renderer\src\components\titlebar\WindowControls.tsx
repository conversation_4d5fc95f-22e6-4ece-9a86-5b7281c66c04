import React, { useState, useEffect, useCallback } from 'react'
import { Minus, Square, Copy, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useWindowControls } from '@/hooks/useWindowControls'
import type { WindowState } from '@/types'

interface WindowControlsProps {
  className?: string
  showOnHover?: boolean
}

export function WindowControls({ className, showOnHover = false }: WindowControlsProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [windowState, setWindowState] = useState<WindowState>({
    isMaximized: false,
    isMinimized: false,
    isFullscreen: false,
    isFocused: true
  })

  const { minimize, maximize, close, getState, isLoading } = useWindowControls()

  // 获取初始窗口状态
  useEffect(() => {
    getState().then((response) => {
      if (response.success && response.data) {
        setWindowState(response.data)
      }
    })
  }, [getState])

  // 监听窗口状态变化
  useEffect(() => {
    const removeListener = window.api?.window.onStateChanged((state: Partial<WindowState>) => {
      setWindowState((prev) => ({ ...prev, ...state }))
    })

    return removeListener
  }, [])

  const handleMinimize = useCallback(async () => {
    const response = await minimize()
    if (!response.success) {
      console.error('最小化失败:', response.error)
    }
  }, [minimize])

  const handleMaximize = useCallback(async () => {
    const response = await maximize()
    if (response.success && response.data) {
      setWindowState((prev) => ({
        ...prev,
        isMaximized: response.data!.isMaximized
      }))
    } else if (!response.success) {
      console.error('最大化失败:', response.error)
    }
  }, [maximize])

  const handleClose = useCallback(async () => {
    const response = await close()
    if (!response.success && response.error !== 'User cancelled') {
      console.error('关闭失败:', response.error)
    }
  }, [close])

  // 根据平台调整样式
  const platform = window.api?.system.getPlatform()
  const isMacOS = platform === 'darwin'
  const isWindows = platform === 'win32'

  // macOS 通常使用系统的交通灯按钮
  if (isMacOS) {
    return null
  }

  const controlsVisible = !showOnHover || isHovered

  return (
    <div
      className={cn(
        'flex items-center h-full',
        showOnHover && 'transition-opacity duration-200',
        showOnHover && !controlsVisible && 'opacity-0',
        className
      )}
      style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 最小化按钮 */}
      <button
        onClick={handleMinimize}
        disabled={isLoading}
        className={cn(
          'flex items-center justify-center h-8 transition-colors duration-150',
          isWindows ? 'w-12' : 'w-10',
          'hover:bg-gray-100',
          'focus:bg-gray-200',
          'disabled:opacity-50'
        )}
        aria-label="最小化窗口"
      >
        <Minus className="h-4 w-4" />
      </button>

      {/* 最大化/还原按钮 */}
      <button
        onClick={handleMaximize}
        disabled={isLoading}
        className={cn(
          'flex items-center justify-center h-8 transition-colors duration-150',
          isWindows ? 'w-12' : 'w-10',
          'hover:bg-gray-100',
          'focus:bg-gray-200',
          'disabled:opacity-50'
        )}
        aria-label={windowState.isMaximized ? '还原窗口' : '最大化窗口'}
      >
        {windowState.isMaximized ? <Copy className="h-4 w-4" /> : <Square className="h-4 w-4" />}
      </button>

      {/* 关闭按钮 */}
      <button
        onClick={handleClose}
        disabled={isLoading}
        className={cn(
          'flex items-center justify-center h-8 transition-colors duration-150',
          isWindows ? 'w-12' : 'w-10',
          'hover:bg-red-500 hover:text-white',
          'focus:bg-red-600 focus:text-white',
          'disabled:opacity-50'
        )}
        aria-label="关闭窗口"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  )
}
