import type Database from 'better-sqlite3'

interface Migration {
  version: number
  name: string
  up: (db: Database.Database) => void
  down?: (db: Database.Database) => void
}

const migrations: Migration[] = [
  {
    version: 1,
    name: 'initial_schema',
    up: (db) => {
      // 创建版本表
      db.exec(`
        CREATE TABLE IF NOT EXISTS schema_version (
          version INTEGER PRIMARY KEY,
          applied_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // 创建标签页表
      db.exec(`
        CREATE TABLE IF NOT EXISTS tabs (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          active INTEGER NOT NULL DEFAULT 0,
          isDirty INTEGER NOT NULL DEFAULT 0,
          isPinned INTEGER NOT NULL DEFAULT 0,
          icon TEXT,
          closable INTEGER NOT NULL DEFAULT 1,
          tooltip TEXT,
          lastModified TEXT,
          metadata TEXT,
          path TEXT,
          type TEXT NOT NULL DEFAULT 'file',
          createdAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // 创建设置表
      db.exec(`
        CREATE TABLE IF NOT EXISTS settings (
          key TEXT PRIMARY KEY,
          value TEXT NOT NULL,
          updatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // 创建窗口状态表
      db.exec(`
        CREATE TABLE IF NOT EXISTS window_state (
          id INTEGER PRIMARY KEY DEFAULT 1,
          isMaximized INTEGER NOT NULL DEFAULT 0,
          isMinimized INTEGER NOT NULL DEFAULT 0,
          isFullscreen INTEGER NOT NULL DEFAULT 0,
          isFocused INTEGER NOT NULL DEFAULT 1,
          bounds TEXT,
          updatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // 创建索引
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_tabs_active ON tabs(active);
        CREATE INDEX IF NOT EXISTS idx_tabs_type ON tabs(type);
        CREATE INDEX IF NOT EXISTS idx_tabs_updated ON tabs(updatedAt);
        CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key);
      `)
    }
  },
  {
    version: 2,
    name: 'add_tab_order',
    up: (db) => {
      // 添加标签页排序字段
      db.exec(`
        ALTER TABLE tabs ADD COLUMN sort_order INTEGER DEFAULT 0
      `)

      // 更新现有标签页的排序
      db.exec(`
        UPDATE tabs SET sort_order = ROWID WHERE sort_order = 0
      `)

      // 创建排序索引
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_tabs_sort_order ON tabs(sort_order)
      `)
    }
  },
  {
    version: 3,
    name: 'add_recent_files',
    up: (db) => {
      // 创建最近文件表
      db.exec(`
        CREATE TABLE IF NOT EXISTS recent_files (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          path TEXT NOT NULL UNIQUE,
          name TEXT NOT NULL,
          lastAccessed TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
          fileSize INTEGER,
          fileType TEXT,
          metadata TEXT
        )
      `)

      // 创建索引
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_recent_files_path ON recent_files(path);
        CREATE INDEX IF NOT EXISTS idx_recent_files_accessed ON recent_files(lastAccessed);
      `)
    }
  }
]

export async function runMigrations(db: Database.Database): Promise<void> {
  try {
    // 获取当前数据库版本
    let currentVersion = 0
    try {
      const result = db.prepare('SELECT MAX(version) as version FROM schema_version').get() as {
        version: number | null
      }
      currentVersion = result?.version || 0
    } catch (error) {
      console.error('Failed to get database version:', error)
      // 如果表不存在，版本为0
      console.log('First run, initializing database...')
    }

    console.log(`Current database version: ${currentVersion}`)

    // 运行需要的迁移
    const pendingMigrations = migrations.filter((m) => m.version > currentVersion)

    if (pendingMigrations.length === 0) {
      console.log('Database is already up to date')
      return
    }

    console.log(`Need to run ${pendingMigrations.length} migrations`)

    // 在事务中运行所有迁移
    const transaction = db.transaction(() => {
      for (const migration of pendingMigrations) {
        console.log(`Running migration: ${migration.name} (version ${migration.version})`)

        try {
          migration.up(db)

          // 记录迁移版本
          db.prepare('INSERT INTO schema_version (version) VALUES (?)').run(migration.version)

          console.log(`Migration ${migration.name} completed`)
        } catch (error) {
          console.error(`Migration ${migration.name} failed:`, error)
          throw error
        }
      }
    })

    transaction()

    console.log('All database migrations completed')
  } catch (error) {
    console.error('Database migration failed:', error)
    throw error
  }
}

export function rollbackMigration(db: Database.Database, targetVersion: number): void {
  try {
    const currentVersion = db
      .prepare('SELECT MAX(version) as version FROM schema_version')
      .get() as { version: number }

    if (currentVersion.version <= targetVersion) {
      console.log('No rollback needed')
      return
    }

    const migrationsToRollback = migrations
      .filter((m) => m.version > targetVersion && m.version <= currentVersion.version)
      .sort((a, b) => b.version - a.version) // 倒序回滚

    const transaction = db.transaction(() => {
      for (const migration of migrationsToRollback) {
        if (migration.down) {
          console.log(`Rolling back migration: ${migration.name} (version ${migration.version})`)
          migration.down(db)
        }

        // 删除版本记录
        db.prepare('DELETE FROM schema_version WHERE version = ?').run(migration.version)
      }
    })

    transaction()

    console.log(`Rollback to version ${targetVersion} completed`)
  } catch (error) {
    console.error('Database rollback failed:', error)
    throw error
  }
}
